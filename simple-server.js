/**
 * Simple Express server for Cloud Run deployment
 * Minimal implementation of bolt2api functionality
 */

const express = require('express');
const cors = require('cors');
const { parse: parseCookie } = require('cookie');
const fetch = require('node-fetch');

const app = express();
const port = process.env.PORT || 8080;

// Middleware
app.use(express.json({ limit: '1mb' }));
app.use(express.urlencoded({ extended: true }));

// CORS configuration
const corsOptions = {
  origin: process.env.ALLOWED_ORIGINS?.split(',') || ['*'],
  credentials: true,
  methods: ['GET', 'POST', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Session-Cookies']
};
app.use(cors(corsOptions));

// Utility functions
function generateConversationId() {
  return Math.random().toString(36).substring(2, 18);
}

function generateMessageId() {
  return Math.random().toString(36).substring(2, 18);
}

function parseBoltCookies(cookieString) {
  const cookies = parseCookie(cookieString);

  // Check for individual account cookies first
  const individualCookies = ['_stackblitz_session', 'sb_session', 'sb_user_id'];
  const hasIndividualCookies = individualCookies.every(name => cookies[name]);

  if (hasIndividualCookies) {
    console.log('🔑 Using individual account authentication');
    return {
      cookies,
      accountType: 'individual',
      userId: cookies.sb_user_id,
      organizationId: cookies.sb_org_id
    };
  }

  // Check for team account cookies (allow partial match for httpOnly session cookies)
  const hasActiveOrganizationId = cookies.activeOrganizationId;
  const hasSessionCookie = cookies.__session;

  if (hasActiveOrganizationId) {
    console.log('🏢 Using team account authentication (partial cookies detected)');
    if (!hasSessionCookie) {
      console.log('⚠️  Missing __session cookie (likely httpOnly) - will attempt authentication anyway');
    }
    return {
      cookies,
      accountType: 'team',
      sessionToken: cookies.__session || 'missing_httponly',
      organizationId: cookies.activeOrganizationId,
      rememberToken: cookies.remember_user_token,
      oauthProvider: cookies.bolt_oauth_provider
    };
  }

  // If neither type is found, throw error
  const missingIndividual = individualCookies.filter(name => !cookies[name]);
  const missingTeam = teamCookies.filter(name => !cookies[name]);

  throw new Error(`Missing required cookies. For individual accounts: ${missingIndividual.join(', ')}. For team accounts: ${missingTeam.join(', ')}`);
}

function createAuthHeaders(session, projectId = null) {
  const cookieString = Object.entries(session.cookies)
    .map(([name, value]) => `${name}=${value}`)
    .join('; ');

  const headers = {
    'Cookie': cookieString,
    'Content-Type': 'application/json',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Referer': 'https://bolt.new/',
    'Origin': 'https://bolt.new',
    'Accept': '*/*',
    'Accept-Language': 'en-US,en;q=0.9',
    'Accept-Encoding': 'gzip, deflate, br, zstd',
    'Sec-Ch-Ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
    'Sec-Ch-Ua-Mobile': '?0',
    'Sec-Ch-Ua-Platform': '"Windows"',
    'Sec-Fetch-Dest': 'empty',
    'Sec-Fetch-Mode': 'cors',
    'Sec-Fetch-Site': 'same-origin',
    'Priority': 'u=1, i'
  };

  // Add team-specific headers
  if (session.accountType === 'team') {
    headers['X-Bolt-Client-Revision'] = 'd65f6d0';
    if (projectId) {
      headers['X-Bolt-Project-Id'] = projectId;
    }
  }

  return headers;
}

async function makeRequestToBolt(endpoint, options) {
  const url = `https://bolt.new${endpoint}`;
  
  const response = await fetch(url, {
    method: options.method || 'GET',
    headers: options.headers,
    body: options.body ? JSON.stringify(options.body) : undefined,
    credentials: 'include'
  });

  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }

  const contentType = response.headers.get('content-type');
  if (contentType?.includes('application/json')) {
    return await response.json();
  } else {
    return await response.text();
  }
}

// Routes

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    name: 'bolt2api',
    version: '1.0.0',
    description: 'Serverless API wrapper for bolt.new chat functionality',
    endpoints: {
      chat: '/api/chat',
      auth: '/api/auth',
      health: '/api/health'
    },
    status: 'running'
  });
});

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// Authentication validation
app.post('/api/auth', async (req, res) => {
  try {
    const sessionCookies = req.headers['x-session-cookies'] || req.body.cookies;

    if (!sessionCookies) {
      return res.status(400).json({
        success: false,
        error: 'missing_session',
        message: 'Session cookies required'
      });
    }

    const session = parseBoltCookies(sessionCookies);
    const headers = createAuthHeaders(session);
    
    // Test authentication with bolt.new
    const tokenData = await makeRequestToBolt('/api/token', {
      method: 'GET',
      headers
    });

    res.json({
      success: true,
      data: {
        valid: true,
        accountType: session.accountType,
        organizationId: session.organizationId,
        userId: session.userId || 'team_user',
        tokenData
      }
    });

  } catch (error) {
    console.error('Auth error:', error);
    res.status(401).json({
      success: false,
      error: 'authentication_error',
      message: error.message
    });
  }
});

// Chat endpoint
app.post('/api/chat', async (req, res) => {
  try {
    const sessionCookies = req.headers['x-session-cookies'] || req.body.sessionCookies;
    
    if (!sessionCookies) {
      return res.status(401).json({
        success: false,
        error: 'authentication_required',
        message: 'Session cookies are required'
      });
    }

    if (!req.body.message) {
      return res.status(400).json({
        success: false,
        error: 'validation_error',
        message: 'Message is required'
      });
    }

    const session = parseBoltCookies(sessionCookies);

    console.log(`💬 Chat request for ${session.accountType} account`);

    // Build request based on account type
    let chatRequest;
    let headers;

    if (session.accountType === 'team') {
      // Team account format - complex conversation structure
      const projectId = req.body.projectId || "********";

      chatRequest = {
        id: req.body.conversationId || generateConversationId(),
        messages: [
          {
            id: generateMessageId(),
            role: "user",
            content: req.body.message,
            rawContent: req.body.message,
            cache: false,
            parts: []
          }
        ],
        isFirstPrompt: req.body.isFirstPrompt || false,
        featurePreviews: req.body.featurePreviews || {
          diffs: false,
          reasoning: false
        },
        errorReasoning: null,
        framework: req.body.framework || "DEFAULT_TO_DEV",
        promptMode: req.body.promptMode || "discussion",
        projectId: projectId,
        stripeStatus: req.body.stripeStatus || "not-configured",
        metrics: req.body.metrics || {
          importFilesLength: 0,
          fileChangesLength: 0
        },
        usesInspectedElement: req.body.usesInspectedElement || false,
        supportIntegrations: req.body.supportIntegrations !== false
      };

      headers = createAuthHeaders(session, projectId);
    } else {
      // Individual account format - simple structure
      chatRequest = {
        message: req.body.message,
        projectId: req.body.projectId,
        context: req.body.context,
        mode: req.body.mode || 'build',
        organizationId: session.organizationId
      };

      headers = createAuthHeaders(session);
    }

    console.log('📤 Sending chat request:', JSON.stringify(chatRequest, null, 2));

    const response = await makeRequestToBolt('/api/chat', {
      method: 'POST',
      headers,
      body: chatRequest
    });

    res.json({
      success: true,
      data: response,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Chat error:', error);
    res.status(500).json({
      success: false,
      error: 'chat_error',
      message: error.message
    });
  }
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'not_found',
    message: 'Endpoint not found',
    availableEndpoints: ['/api/chat', '/api/auth', '/health']
  });
});

// Error handler
app.use((error, req, res, next) => {
  console.error('Unhandled error:', error);
  res.status(500).json({
    success: false,
    error: 'internal_error',
    message: 'Internal server error'
  });
});

// Start server
app.listen(port, '0.0.0.0', () => {
  console.log(`🚀 bolt2api server running on port ${port}`);
  console.log(`📊 Health check: http://localhost:${port}/health`);
  console.log(`💬 Chat API: http://localhost:${port}/api/chat`);
  console.log(`🔐 Auth API: http://localhost:${port}/api/auth`);
});

module.exports = app;
