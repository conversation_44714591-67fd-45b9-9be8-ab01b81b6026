# Bolt2API Wrapper Fixes Summary

## Issues Fixed

### 1. ✅ Payload Structure Corrected
**Before:** Simplified payload with wrong fields
```json
{
  "message": "...",
  "organizationId": "...",
  "projectId": "...",
  "mode": "...",
  "context": "...",
  "promptMode": "..."
}
```

**After:** Exact browser payload structure
```json
{
  "id": "Y5CZS3D6b452VCfQ",
  "errorReasoning": null,
  "featurePreviews": {
    "diffs": false,
    "reasoning": false
  },
  "framework": "DEFAULT_TO_DEV",
  "isFirstPrompt": false,
  "messages": [...],
  "metrics": {
    "importFilesLength": 0,
    "fileChangesLength": 0
  },
  "projectId": "49956303",
  "promptMode": "discussion",
  "stripeStatus": "not-configured",
  "supportIntegrations": true,
  "usesInspectedElement": false
}
```

### 2. ✅ Cookie Handling Fixed
**Before:** 
- Incorrectly decoded cookies with `decodeURIComponent()`
- Attempted base64 decoding of `activeOrganizationId`
- Missing session token extraction

**After:**
- Keeps original cookie values as-is (URL-encoded when needed)
- Properly extracts `_session` token
- Maintains `activeOrganizationId` in original format (`Mjl4NTE%3D`)

### 3. ✅ Headers Corrected
**Before:** Included HTTP/2 pseudo-headers
```javascript
{
  'authority': 'bolt.new',
  'method': 'POST',
  'path': '/api/chat',
  'scheme': 'https',
  // ... other headers
}
```

**After:** Standard HTTP headers only
```javascript
{
  'Host': 'bolt.new',
  'Accept': '*/*',
  'Accept-Encoding': 'gzip, deflate, br, zstd',
  'Accept-Language': 'en-US,en;q=0.9',
  'Content-Type': 'application/json',
  'Cookie': cookieString,
  'User-Agent': 'Mozilla/5.0 (...)'
}
```

## Key Changes Made

1. **`corrected-bolt2api.js`**: Updated to match exact browser request format
2. **`test-team-chat-corrected.json`**: New test file with correct payload structure
3. **`sample-cookies.txt`**: Example of correct cookie format from browser
4. **`test-corrected-wrapper.js`**: Test script to verify the fixes

## How to Test

1. **Start the server:**
   ```bash
   node corrected-bolt2api.js
   ```

2. **Run the test:**
   ```bash
   node test-corrected-wrapper.js
   ```

3. **Manual test with cURL:**
   ```bash
   curl 'http://localhost:8080/api/chat' \
     -X POST \
     -H 'Content-Type: application/json' \
     -H 'Cookie: [YOUR_ACTUAL_COOKIES]' \
     --data @test-team-chat-corrected.json
   ```

## Next Steps

1. Replace the sample cookies in `sample-cookies.txt` with your actual bolt.new session cookies
2. Test with real cookies to ensure authentication works
3. Deploy to Google Cloud Run for production testing

## Files Modified

- ✅ `corrected-bolt2api.js` - Main wrapper with all fixes
- ✅ `test-team-chat-corrected.json` - Correct payload format
- ✅ `sample-cookies.txt` - Example cookie format
- ✅ `test-corrected-wrapper.js` - Test script
- ✅ `FIXES-SUMMARY.md` - This summary

The wrapper now matches the exact browser request format and should work correctly with bolt.new's API.
