# Extract Fresh Authentication Data from Browser

## Step 1: Capture Successful Browser Request

1. **Open bolt.new in Chrome/Edge**
2. **Open DevTools** (F12)
3. **Go to Network tab**
4. **Clear network log** (🚫 button)
5. **Make a chat request** in bolt.new UI
6. **Find the `/api/chat` request** in Network tab
7. **Right-click the request** → **Copy** → **Copy as cURL (bash)**

## Step 2: Extract Authentication Headers

From the cURL command, extract these key components:

### A. <PERSON>ie Header
Look for `-H 'Cookie: ...'` in the cURL command. Copy the ENTIRE cookie string.

**Example:**
```bash
-H 'Cookie: __hssc=69929231.2.1749947958589; __hssrc=1; _session=eyJkIjoiZVR6SUdsdTlaZlh5...; activeOrganizationId=Mjl4NTE%3D; ...'
```

### B. CSRF/Auth Headers
Look for any of these headers in the cURL:
- `-H 'x-csrf-token: ...'`
- `-H 'x-bolt-client-revision: ...'`
- `-H 'x-bolt-project-id: ...'`
- `-H 'authorization: ...'`

### C. Other Required Headers
Note these headers that might be required:
- `-H 'referer: https://bolt.new/...'`
- `-H 'origin: https://bolt.new'`
- `-H 'user-agent: ...'`

## Step 3: Verify Cookie Format

Ensure your cookie string:
- ✅ Contains `_session=...` (main auth cookie)
- ✅ Contains `activeOrganizationId=...` (for team accounts)
- ✅ Uses `; ` (semicolon + space) as separator
- ❌ Does NOT include `Path=`, `Domain=`, `Secure`, `HttpOnly` attributes
- ❌ Does NOT have trailing semicolon

**Correct format:**
```
name1=value1; name2=value2; name3=value3
```

**Incorrect format:**
```
name1=value1; Path=/; Domain=.bolt.new; Secure; HttpOnly; name2=value2;
```

## Step 4: Test with cURL First

Before updating the proxy, test the extracted headers directly:

```bash
# Replace with your extracted headers
curl 'https://bolt.new/api/chat' \
  -X POST \
  -H 'Content-Type: application/json' \
  -H 'Cookie: [YOUR_EXTRACTED_COOKIES]' \
  -H 'x-csrf-token: [IF_PRESENT]' \
  -H 'referer: https://bolt.new/' \
  -H 'origin: https://bolt.new' \
  --data-raw '{
    "id": "test123",
    "errorReasoning": null,
    "featurePreviews": {"diffs": false, "reasoning": false},
    "framework": "DEFAULT_TO_DEV",
    "isFirstPrompt": false,
    "messages": [{"id": "msg1", "role": "user", "content": "Hello"}],
    "metrics": {"importFilesLength": 0, "fileChangesLength": 0},
    "projectId": "********",
    "promptMode": "discussion",
    "stripeStatus": "not-configured",
    "supportIntegrations": true,
    "usesInspectedElement": false
  }'
```

**Expected Results:**
- ✅ **200 OK**: Authentication works, proceed to update proxy
- ❌ **401 Unauthorized**: Cookie expired or missing required header
- ❌ **403 Forbidden**: CSRF token required or invalid

## Step 5: Update sample-cookies.txt

Once you have working authentication, update the file:

```bash
# Save ONLY the cookie values (no -H 'Cookie: ' prefix)
echo "name1=value1; name2=value2; name3=value3" > sample-cookies.txt
```

## Step 6: Check for Additional Requirements

If still getting 401, check for:

### A. CSRF Token
Some requests require a CSRF token. Look in the browser request for:
- Header: `x-csrf-token: abc123`
- Or in HTML: `<meta name="csrf-token" content="abc123">`

### B. Project-Specific Headers
For team accounts, check for:
- `x-bolt-project-id: ********`
- `x-bolt-client-revision: d65f6d0`

### C. Session Validation
The `_session` cookie might be:
- **Expired**: Get fresh cookies after new login
- **IP-locked**: Use from same IP/location
- **Browser-locked**: Include exact User-Agent

## Troubleshooting Checklist

- [ ] Extracted cookies from successful browser request
- [ ] Verified `_session` cookie is present and recent
- [ ] Tested with cURL using exact browser headers
- [ ] Checked for CSRF tokens or additional auth headers
- [ ] Ensured cookie format has no domain/path attributes
- [ ] Confirmed no trailing semicolon in cookie string
- [ ] Used same User-Agent as browser if required

## Next Steps

1. **Extract fresh auth data** using steps above
2. **Test with cURL** to verify authentication works
3. **Update proxy** with any missing headers
4. **Test deployed service** with fresh authentication

---

**Need Help?** Share the cURL command (with sensitive values redacted) and we can identify what's missing!
