{"version": 3, "file": "runtimes.js", "sourceRoot": "", "sources": ["../../src/runtimes.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+BAA4B;AAC5B,kDAAgC;AAChC,kEAAwC;AACxC,mCAA0C;AAC1C,uCAA+E;AAG/E,uDAAyC;AACzC,+DAAiD;AACjD,+DAAiD;AACjD,gEAAkD;AAClD,gEAAkD;AAClD,gEAAkD;AAClD,+DAAiD;AACjD,4DAA8C;AAC9C,+DAAiD;AACjD,+DAAiD;AAEjD,MAAM,KAAK,GAAG,IAAA,eAAW,EAAC,sBAAsB,CAAC,CAAC;AAClD,MAAM,WAAW,GAAG,IAAA,WAAI,EAAC,SAAS,EAAE,UAAU,CAAC,CAAC;AAUnC,QAAA,QAAQ,GAAa,EAAE,CAAC;AAExB,QAAA,WAAW,GAAG,IAAA,uBAAW,EAAC,gBAAgB,CAAC,CAAC,KAAK,EAAE,CAAC;AAEjE,SAAS,aAAa,CACrB,QAAkB,EAClB,IAAY,EACZ,GAAiB;IAEjB,MAAM,OAAO,mBACZ,IAAI,EACJ,UAAU,EAAE,IAAA,WAAI,EAAC,WAAW,EAAE,IAAI,CAAC,IAChC,GAAG,CACN,CAAC;IACF,QAAQ,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC;AAC1B,CAAC;AAED,aAAa,CAAC,gBAAQ,EAAE,UAAU,CAAC,CAAC;AACpC,aAAa,CAAC,gBAAQ,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;AACvC,aAAa,CAAC,gBAAQ,EAAE,QAAQ,CAAC,CAAC;AAClC,aAAa,CAAC,gBAAQ,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;AAC/C,aAAa,CAAC,gBAAQ,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;AAC/C,aAAa,CAAC,gBAAQ,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC;AAChD,aAAa,CAAC,gBAAQ,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC;AAChD,aAAa,CAAC,gBAAQ,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC;AAChD,aAAa,CAAC,gBAAQ,EAAE,QAAQ,CAAC,CAAC;AAClC,aAAa,CAAC,gBAAQ,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;AAC/C,aAAa,CAAC,gBAAQ,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;AAC5C,aAAa,CAAC,gBAAQ,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;AAC/C,aAAa,CAAC,gBAAQ,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;AAE/C;;;GAGG;AACH,SAAe,mBAAmB,CAAC,CAAS;;QAC3C,IAAI;YACH,OAAO,MAAM,IAAA,mBAAQ,EAAC,CAAC,EAAE,OAAO,CAAC,CAAC;SAClC;QAAC,OAAO,GAAG,EAAE;YACb,IAAI,GAAG,CAAC,IAAI,KAAK,QAAQ,EAAE;gBAC1B,OAAO,IAAI,CAAC;aACZ;YACD,MAAM,GAAG,CAAC;SACV;IACF,CAAC;CAAA;AAED,MAAM,kBAAkB,GAAiC,IAAI,GAAG,EAAE,CAAC;AAEnE;;;;GAIG;AACH,SAAe,oBAAoB,CAAC,GAAW;;QAC9C,KAAK,CAAC,yBAAyB,EAAE,GAAG,CAAC,CAAC;QACtC,MAAM,IAAI,GAAG,IAAA,mBAAU,EAAC,QAAQ,CAAC,CAAC;QAClC,MAAM,sBAAsB,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QACxC,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC/B,KAAK,CAAC,mCAAmC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QACrD,OAAO,GAAG,CAAC;IACZ,CAAC;CAAA;AACD,SAAe,sBAAsB,CAAC,GAAW,EAAE,IAAU;;QAC5D,MAAM,OAAO,GAAG,MAAM,IAAA,kBAAO,EAAC,GAAG,CAAC,CAAC;QACnC,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE;YAC5B,MAAM,OAAO,GAAG,IAAA,WAAI,EAAC,GAAG,EAAE,KAAK,CAAC,CAAC;YACjC,MAAM,CAAC,GAAG,MAAM,IAAA,gBAAK,EAAC,OAAO,CAAC,CAAC;YAC/B,IAAI,CAAC,CAAC,WAAW,EAAE,EAAE;gBACpB,MAAM,sBAAsB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;aAC5C;iBAAM;gBACN,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAQ,EAAC,OAAO,CAAC,CAAC;gBACzC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;aACtB;SACD;IACF,CAAC;CAAA;AACD,SAAS,mBAAmB,CAAC,GAAW;IACvC,qEAAqE;IACrE,oEAAoE;IACpE,IAAI,CAAC,GAAG,kBAAkB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACpC,IAAI,CAAC,CAAC,EAAE;QACP,CAAC,GAAG,oBAAoB,CAAC,GAAG,CAAC,CAAC;QAC9B,kBAAkB,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;KAC/B;IACD,OAAO,CAAC,CAAC;AACV,CAAC;AAED;;;GAGG;AACH,SAAe,IAAI,CAAC,GAAW,EAAE,IAAY;;QAC5C,KAAK,CAAC,cAAc,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;QACjC,MAAM,CAAC,OAAO,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,IAAA,kBAAO,EAAC,GAAG,CAAC,EAAE,IAAA,iBAAM,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClE,KAAK,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;QAE9B,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE;YAC5B,MAAM,OAAO,GAAG,IAAA,WAAI,EAAC,GAAG,EAAE,KAAK,CAAC,CAAC;YACjC,MAAM,QAAQ,GAAG,IAAA,WAAI,EAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YACnC,MAAM,CAAC,GAAG,MAAM,IAAA,gBAAK,EAAC,OAAO,CAAC,CAAC;YAC/B,IAAI,CAAC,CAAC,WAAW,EAAE,EAAE;gBACpB,MAAM,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;aAC9B;iBAAM;gBACN,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAQ,EAAC,OAAO,CAAC,CAAC;gBACzC,MAAM,IAAA,oBAAS,EAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;aACtD;SACD;IACF,CAAC;CAAA;AAED,wEAAwE;AACxE,MAAM,YAAY,GAAgC,IAAI,GAAG,EAAE,CAAC;AAE5D,SAAe,kBAAkB,CAAC,OAAgB;;QACjD,MAAM,QAAQ,GAAG,IAAA,WAAI,EAAC,mBAAW,EAAE,UAAU,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;QAC7D,MAAM,YAAY,GAAG,IAAA,WAAI,EAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;QAClD,MAAM,CAAC,gBAAgB,EAAE,UAAU,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YACxD,mBAAmB,CAAC,YAAY,CAAC;YACjC,mBAAmB,CAAC,OAAO,CAAC,UAAU,CAAC;SACvC,CAAC,CAAC;QACH,OAAO,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAC5B,IAAI,gBAAgB,KAAK,UAAU,EAAE;YACpC,KAAK,CACJ,yCAAyC,EACzC,OAAO,CAAC,IAAI,EACZ,QAAQ,CACR,CAAC;SACF;aAAM;YACN,KAAK,CAAC,+BAA+B,EAAE,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YAC/D,IAAI;gBACH,MAAM,IAAA,iBAAM,EAAC,QAAQ,CAAC,CAAC;gBAEvB,mEAAmE;gBACnE,6DAA6D;gBAC7D,iEAAiE;gBACjE,qBAAqB;gBACrB,MAAM,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;gBAEzC,oDAAoD;gBACpD,IAAI,OAAO,OAAO,CAAC,IAAI,KAAK,UAAU,EAAE;oBACvC,MAAM,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;iBAC5B;gBAED,MAAM,IAAA,oBAAS,EAAC,IAAA,WAAI,EAAC,QAAQ,EAAE,YAAY,CAAC,EAAE,UAAU,CAAC,CAAC;aAC1D;YAAC,OAAO,GAAG,EAAE;gBACb,KAAK,CACJ,yDAAyD,EACzD,OAAO,CAAC,IAAI,EACZ,GAAG,EACH,QAAQ,CACR,CAAC;gBACF,IAAI;oBACH,MAAM,IAAA,iBAAM,EAAC,QAAQ,CAAC,CAAC;iBACvB;gBAAC,OAAO,IAAI,EAAE;oBACd,KAAK,CAAC,kCAAkC,EAAE,IAAI,CAAC,CAAC;iBAChD;gBACD,MAAM,GAAG,CAAC;aACV;SACD;IACF,CAAC;CAAA;AAED,SAAsB,iBAAiB,CACtC,MAAwB;;QAExB,IAAI,OAAgB,CAAC;QACrB,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;YAC/B,OAAO,GAAG,gBAAQ,CAAC,MAAM,CAAC,CAAC;YAC3B,IAAI,CAAC,OAAO,EAAE;gBACb,MAAM,IAAI,KAAK,CAAC,qCAAqC,MAAM,GAAG,CAAC,CAAC;aAChE;SACD;aAAM;YACN,OAAO,GAAG,MAAM,CAAC;SACjB;QAED,IAAI,CAAC,GAAG,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAClC,IAAI,CAAC,EAAE;YACN,MAAM,CAAC,CAAC;SACR;aAAM;YACN,CAAC,GAAG,kBAAkB,CAAC,OAAO,CAAC,CAAC;YAChC,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;YAE7B,IAAI;gBACH,MAAM,CAAC,CAAC;aACR;oBAAS;gBACT,2EAA2E;gBAC3E,uEAAuE;gBACvE,wEAAwE;gBACxE,iEAAiE;gBACjE,YAAY,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;aAC7B;SACD;QAED,OAAO,OAAO,CAAC;IAChB,CAAC;CAAA;AAhCD,8CAgCC"}