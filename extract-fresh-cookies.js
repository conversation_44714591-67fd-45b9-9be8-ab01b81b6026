/**
 * Cookie extraction helper for bolt.new team accounts
 * 
 * INSTRUCTIONS:
 * 1. Open bolt.new in your browser
 * 2. Make sure you're logged in to your team account
 * 3. Open DevTools (F12)
 * 4. Go to Console tab
 * 5. Paste this script and run it
 * 6. Copy the output and use it in your test
 */

console.log('🍪 Extracting bolt.new cookies...\n');

// Get all cookies for bolt.new domain
const cookies = document.cookie;

if (!cookies) {
  console.error('❌ No cookies found! Make sure you are logged in to bolt.new');
} else {
  console.log('✅ Found cookies for bolt.new\n');
  
  // Parse cookies into object
  const cookieObj = {};
  cookies.split(';').forEach(cookie => {
    const [name, value] = cookie.trim().split('=');
    if (name && value) {
      cookieObj[name] = value;
    }
  });
  
  // Check for team account cookies
  const teamCookies = ['__session', 'activeOrganizationId'];
  const hasTeamCookies = teamCookies.every(name => cookieObj[name]);
  
  // Check for individual account cookies
  const individualCookies = ['_stackblitz_session', 'sb_session', 'sb_user_id'];
  const hasIndividualCookies = individualCookies.every(name => cookieObj[name]);
  
  console.log('🔍 Account type detection:');
  console.log('  Team account cookies present:', hasTeamCookies);
  console.log('  Individual account cookies present:', hasIndividualCookies);
  console.log('');
  
  if (hasTeamCookies) {
    console.log('🏢 TEAM ACCOUNT DETECTED');
    console.log('Required cookies found:');
    teamCookies.forEach(name => {
      if (cookieObj[name]) {
        console.log(`  ✅ ${name}: ${cookieObj[name].substring(0, 20)}...`);
      }
    });
    
    console.log('\nOptional team cookies:');
    ['remember_user_token', 'bolt_oauth_provider'].forEach(name => {
      if (cookieObj[name]) {
        console.log(`  ✅ ${name}: ${cookieObj[name].substring(0, 20)}...`);
      } else {
        console.log(`  ❌ ${name}: not found`);
      }
    });
    
  } else if (hasIndividualCookies) {
    console.log('👤 INDIVIDUAL ACCOUNT DETECTED');
    console.log('Required cookies found:');
    individualCookies.forEach(name => {
      if (cookieObj[name]) {
        console.log(`  ✅ ${name}: ${cookieObj[name].substring(0, 20)}...`);
      }
    });
    
  } else {
    console.log('❌ NO VALID ACCOUNT TYPE DETECTED');
    console.log('Missing required cookies for both account types');
    console.log('\nFor team accounts, need: __session, activeOrganizationId');
    console.log('For individual accounts, need: _stackblitz_session, sb_session, sb_user_id');
  }
  
  console.log('\n📋 COPY THIS COOKIE STRING FOR YOUR TEST:');
  console.log('==========================================');
  console.log(cookies);
  console.log('==========================================');
  
  console.log('\n🔧 JavaScript array format:');
  console.log('const cookies = [');
  cookies.split(';').forEach(cookie => {
    const trimmed = cookie.trim();
    if (trimmed) {
      console.log(`  '${trimmed}',`);
    }
  });
  console.log('].join("; ");');
  
  console.log('\n💡 Next steps:');
  console.log('1. Copy the cookie string above');
  console.log('2. Replace the cookies variable in your test script');
  console.log('3. Run your authentication test');
}

// Also try to get current user info
fetch('/api/token')
  .then(response => {
    if (response.ok) {
      return response.json();
    } else {
      throw new Error(`HTTP ${response.status}`);
    }
  })
  .then(data => {
    console.log('\n🔐 Current authentication status:');
    console.log('✅ Successfully authenticated with bolt.new');
    console.log('Token data:', data);
  })
  .catch(error => {
    console.log('\n🔐 Current authentication status:');
    console.log('❌ Not authenticated or token endpoint failed:', error.message);
    console.log('Make sure you are logged in to bolt.new');
  });
