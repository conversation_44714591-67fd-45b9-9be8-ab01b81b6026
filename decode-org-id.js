// Decode the organization ID from the cookie
const encodedOrgId = "MjI4NTE%3D";

// URL decode first
const urlDecoded = decodeURIComponent(encodedOrgId);
console.log("URL decoded:", urlDecoded);

// Base64 decode
const base64Decoded = Buffer.from(urlDecoded, 'base64').toString('utf-8');
console.log("Base64 decoded:", base64Decoded);

// Check if it matches the expected org ID from network analysis
const expectedOrgId = "22851";
console.log("Expected from network analysis:", expectedOrgId);
console.log("Match:", base64Decoded === expectedOrgId);
