// Corrected bolt2api that matches EXACTLY what the browser sends
const express = require('express');
const app = express();
const port = process.env.PORT || 8080;

// Middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// CORS middleware
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization, Cookie');
  
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});

// Utility functions
function parseBoltCookies(cookieString) {
  const cookies = {};
  let organizationId = null;
  let accountType = 'individual';
  
  if (cookieString) {
    cookieString.split(';').forEach(cookie => {
      const [name, value] = cookie.trim().split('=');
      if (name && value) {
        cookies[name] = decodeURIComponent(value);
        
        // Check for organization ID to determine account type
        if (name === 'activeOrganizationId' && value && value !== 'null') {
          organizationId = value;
          accountType = 'team';
          
          // Decode the base64 organization ID
          try {
            const decoded = Buffer.from(organizationId, 'base64').toString('utf-8');
            organizationId = decoded;
          } catch (e) {
            // If decoding fails, use as-is
          }
        }
      }
    });
  }
  
  return {
    cookies,
    organizationId,
    accountType
  };
}

function createBrowserHeaders(session) {
  const cookieString = Object.entries(session.cookies)
    .map(([name, value]) => `${name}=${value}`)
    .join('; ');

  // Exact headers from the browser request
  return {
    'authority': 'bolt.new',
    'method': 'POST',
    'path': '/api/chat',
    'scheme': 'https',
    'accept': '*/*',
    'accept-encoding': 'gzip, deflate, br, zstd',
    'accept-language': 'en-US,en;q=0.9',
    'content-type': 'application/json',
    'cookie': cookieString,
    'origin': 'https://bolt.new',
    'priority': 'u=1, i',
    'referer': 'https://bolt.new/~/sb1-sum5bqy5',
    'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-origin',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'x-bolt-client-revision': 'd65f6d0',
    'x-bolt-project-id': '********'
  };
}

async function makeRequestToBolt(endpoint, options) {
  const url = `https://bolt.new${endpoint}`;
  
  console.log(`🌐 Making request to: ${url}`);
  console.log('📋 Headers:', JSON.stringify(options.headers, null, 2));
  console.log('📝 Body:', JSON.stringify(options.body, null, 2));
  
  const response = await fetch(url, {
    method: options.method || 'GET',
    headers: options.headers,
    body: options.body ? JSON.stringify(options.body) : undefined
  });
  
  console.log(`📊 Response status: ${response.status}`);
  
  if (!response.ok) {
    const errorText = await response.text();
    console.error('❌ Bolt.new error response:', errorText);
    throw new Error(`Bolt.new API error: ${response.status} - ${errorText}`);
  }
  
  const data = await response.json();
  console.log('✅ Bolt.new response received');
  return data;
}

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'corrected bolt2api is running',
    timestamp: new Date().toISOString(),
    version: '2.0.0'
  });
});

// Chat endpoint - CORRECTED to match browser exactly
app.post('/api/chat', async (req, res) => {
  try {
    console.log('📥 Received chat request');
    console.log('🍪 Raw cookies:', req.headers.cookie);
    console.log('📝 Request body:', JSON.stringify(req.body, null, 2));
    
    const sessionCookies = req.headers.cookie;
    if (!sessionCookies) {
      console.log('❌ No session cookies found');
      return res.status(401).json({
        success: false,
        error: 'missing_session',
        message: 'Session cookies required'
      });
    }
    
    const session = parseBoltCookies(sessionCookies);
    console.log(`💬 Chat request for ${session.accountType} account`);
    console.log(`🏢 Organization ID: ${session.organizationId}`);

    // Create the EXACT payload structure that the browser sends
    const chatRequest = {
      message: req.body.message,
      organizationId: session.organizationId || "22851", // Use decoded org ID
      projectId: req.body.projectId || "********",
      mode: req.body.mode || "build",
      context: req.body.context || "",
      promptMode: req.body.promptMode || "discussion"
    };

    console.log('📤 Sending chat request:', JSON.stringify(chatRequest, null, 2));

    const headers = createBrowserHeaders(session);
    
    const response = await makeRequestToBolt('/api/chat', {
      method: 'POST',
      headers,
      body: chatRequest
    });

    res.json({
      success: true,
      data: response,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Chat error:', error);
    res.status(500).json({
      success: false,
      error: 'chat_error',
      message: error.message
    });
  }
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'not_found',
    message: 'Endpoint not found',
    availableEndpoints: ['/api/chat', '/health']
  });
});

// Error handler
app.use((error, req, res, next) => {
  console.error('Unhandled error:', error);
  res.status(500).json({
    success: false,
    error: 'internal_error',
    message: 'Internal server error'
  });
});

// Start server
app.listen(port, '0.0.0.0', () => {
  console.log(`🚀 Corrected bolt2api server running on port ${port}`);
  console.log(`📊 Health check: http://localhost:${port}/health`);
  console.log(`💬 Chat API: http://localhost:${port}/api/chat`);
  console.log('🔧 Now matches EXACT browser request structure!');
});

module.exports = app;
