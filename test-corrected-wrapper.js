const fs = require('fs');

// Test the corrected bolt2api wrapper
async function testCorrectedWrapper() {
  console.log('🧪 Testing corrected bolt2api wrapper...');
  
  // Load test payload
  const testPayload = JSON.parse(fs.readFileSync('./test-team-chat-corrected.json', 'utf8'));
  
  // Sample cookies (replace with your actual cookies)
  const sampleCookies = fs.readFileSync('./sample-cookies.txt', 'utf8').trim();
  
  console.log('📋 Test payload:', JSON.stringify(testPayload, null, 2));
  console.log('🍪 Sample cookies length:', sampleCookies.length);
  
  try {
    // Test local server (if running)
    const response = await fetch('http://localhost:8080/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': sampleCookies
      },
      body: JSON.stringify(testPayload)
    });
    
    console.log('📊 Response status:', response.status);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Success:', JSON.stringify(data, null, 2));
    } else {
      const errorText = await response.text();
      console.log('❌ Error response:', errorText);
    }
    
  } catch (error) {
    console.error('🚨 Request failed:', error.message);
    console.log('💡 Make sure the server is running: node corrected-bolt2api.js');
  }
}

// Test health endpoint
async function testHealth() {
  try {
    const response = await fetch('http://localhost:8080/health');
    const data = await response.json();
    console.log('💚 Health check:', data);
  } catch (error) {
    console.error('❤️‍🩹 Health check failed:', error.message);
  }
}

// Run tests
async function runTests() {
  console.log('🚀 Starting tests...\n');
  
  await testHealth();
  console.log('\n---\n');
  await testCorrectedWrapper();
  
  console.log('\n🏁 Tests completed!');
}

runTests().catch(console.error);
